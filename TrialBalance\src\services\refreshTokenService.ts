/**
 * Xero Token Refresh Service
 * 
 * Handles OAuth token refresh for Xero API access
 */

import { PrismaClient } from '@prisma/client';
import axios from 'axios';
import { getXeroConfig } from '../config/environment';
// Removed individual API call logging - using only main Lambda execution logging

const prisma = new PrismaClient();

/**
 * Refresh Xero access token if needed
 * 
 * @param companyId - Company identifier
 * @returns Valid access token
 * @throws Error if token refresh fails
 */
export async function refreshXeroToken(companyId: string): Promise<string> {
    console.log(`🔄 Checking Xero token for company: ${companyId}`);
    let refreshStartTime = Date.now();

    try {
        // Get company integration data
        const company = await prisma.company.findUnique({
            where: { Id: companyId },
            select: {
                Id: true,
                XeroAccessToken: true,
                XeroRefreshToken: true,
                XeroTokenExpiry: true,
                XeroRefreshTokenExpiry: true,
            },
        });

        if (!company) {
            throw new Error(`Company not found: ${companyId}`);
        }

        if (!company.XeroAccessToken || !company.XeroRefreshToken) {
            throw new Error(`Missing Xero tokens for company: ${companyId}`);
        }

        // Check if access token is still valid (with 5-minute buffer)
        const now = new Date();
        const tokenExpiry = company.XeroTokenExpiry;
        const bufferTime = 5 * 60 * 1000; // 5 minutes in milliseconds

        if (tokenExpiry && tokenExpiry.getTime() > now.getTime() + bufferTime) {
            console.log('✅ Access token is still valid');
            return company.XeroAccessToken;
        }

        // Check if refresh token is still valid
        const refreshTokenExpiry = company.XeroRefreshTokenExpiry;
        if (refreshTokenExpiry && refreshTokenExpiry.getTime() <= now.getTime()) {
            throw new Error(`Refresh token expired for company: ${companyId}. Re-authentication required.`);
        }

        console.log('🔄 Access token expired, refreshing...');

        // Refresh the access token
        const { clientId, clientSecret } = getXeroConfig();
        const tokenUrl = 'https://identity.xero.com/connect/token';
        refreshStartTime = Date.now();

        const response = await axios.post(tokenUrl,
            new URLSearchParams({
                grant_type: 'refresh_token',
                refresh_token: company.XeroRefreshToken,
            }).toString(),
            {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Authorization': `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`,
                },
                timeout: 30000, // 30 seconds
            }
        );

        const tokenData = response.data;

        // Token refresh success - logged at Lambda level only

        if (!tokenData.access_token || !tokenData.refresh_token) {
            throw new Error('Invalid token response from Xero');
        }

        // Calculate new expiry times
        const newTokenExpiry = new Date(now.getTime() + (tokenData.expires_in * 1000));
        const newRefreshTokenExpiry = new Date(now.getTime() + (60 * 24 * 60 * 60 * 1000)); // 60 days
        console.log("newTokenExpiry", tokenData);
        // Update tokens in database
        let updatedCompany = await prisma.company.update({
            where: { Id: companyId },
            data: {
                XeroAccessToken: tokenData.access_token,
                XeroRefreshToken: tokenData.refresh_token,
                XeroTokenExpiry: newTokenExpiry,
                XeroRefreshTokenExpiry: newRefreshTokenExpiry,
                UpdatedAt: now,
            },
        });

        console.log('✅ Successfully refreshed Xero tokens');
        return tokenData.access_token;

    } catch (error: any) {
        console.error('❌ Failed to refresh Xero token:', {
            companyId,
            error: error.message,
            status: error.response?.status,
            statusText: error.response?.statusText,
        });

        // Token refresh failure - logged at Lambda level only

        // Handle specific error cases
        if (error.response?.status === 400) {
            const errorData = error.response.data;
            if (errorData.error === 'invalid_grant') {
                throw new Error(`Refresh token is invalid or expired for company: ${companyId}. Re-authentication required.`);
            }
        }

        throw new Error(`Token refresh failed for company: ${companyId}. ${error.message}`);
    } finally {
        await prisma.$disconnect();
    }
}

/**
 * Validate token expiry and determine if refresh is needed
 * 
 * @param tokenExpiry - Token expiry date
 * @param bufferMinutes - Buffer time in minutes before expiry
 * @returns true if token needs refresh
 */
export function needsTokenRefresh(tokenExpiry: Date | null, bufferMinutes: number = 5): boolean {
    if (!tokenExpiry) {
        return true;
    }

    const now = new Date();
    const bufferTime = bufferMinutes * 60 * 1000;

    return tokenExpiry.getTime() <= now.getTime() + bufferTime;
}

/**
 * Check if refresh token is expired
 * 
 * @param refreshTokenExpiry - Refresh token expiry date
 * @returns true if refresh token is expired
 */
export function isRefreshTokenExpired(refreshTokenExpiry: Date | null): boolean {
    if (!refreshTokenExpiry) {
        return true;
    }

    const now = new Date();
    return refreshTokenExpiry.getTime() <= now.getTime();
}
