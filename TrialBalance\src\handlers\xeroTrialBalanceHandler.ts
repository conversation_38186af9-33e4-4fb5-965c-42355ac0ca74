/**
 * Xero Trial Balance Data Synchronization Handler
 *
 * This AWS Lambda handler synchronizes Trial Balance data from Xero API with intelligent
 * sync strategy based on existing data.
 *
 * Sync Strategy:
 * - Initial Sync: 5 years (60 months) of historical data when no data exists
 * - Regular Sync: 13 months (financial year + 1 month) for ongoing updates
 *
 * Key Features:
 * - Intelligent sync period determination based on existing data
 * - Strict rate limiting (1 concurrent for initial, 2 for regular sync)
 * - Comprehensive error handling and logging
 * - Support for both API Gateway and SQS events
 * - Transaction-safe database operations
 * - Automatic Xero token refresh
 *
 * Usage:
 * - API Gateway: POST /trial-balance/sync with { companyId: "uuid" }
 * - SQS: Queue message with { companyId: "uuid", tenantId?: "string" }
 *
 * Environment Variables Required:
 * - DATABASE_URL: PostgreSQL connection string
 * - XERO_CLIENT_ID: Xero OAuth client ID
 * - XERO_CLIENT_SECRET: Xero OAuth client secret
 * - XERO_REDIRECT_URI: Xero OAuth redirect URI
 * - XERO_BASE_URL: Xero API base URL (optional, defaults to production)
 */

import {
    APIGatewayProxyEvent,
    Context,
    SQSEvent,
    APIGatewayProxyResult,
} from 'aws-lambda';
import { XeroRequestData, ValidationError } from '../types';
import { processTrialBalanceRequest } from '../services/trialBalanceService';
import { parseRequestData } from '../utils/requestParser';
// Load environment variables
import dotenv from 'dotenv';
dotenv.config();

/**
 * AWS Lambda Handler for Trial Balance Synchronization
 *
 * Handles both API Gateway and SQS events for Trial Balance data synchronization.
 * Supports both direct API calls and asynchronous queue processing.
 *
 * Event Types:
 * - API Gateway: Direct synchronous processing with HTTP response
 * - SQS: Asynchronous batch processing from queue messages
 *
 * @param {APIGatewayProxyEvent | SQSEvent} event - AWS event (API Gateway or SQS)
 * @param {Context} context - AWS Lambda execution context
 * @returns {Promise<APIGatewayProxyResult | void>} HTTP response for API Gateway, void for SQS
 */
export const handler = async (
    event: APIGatewayProxyEvent | SQSEvent,
    context: Context
): Promise<APIGatewayProxyResult | void> => {
    console.log('🚀 Trial Balance Lambda handler started');
    console.log('📋 Event type:', 'Records' in event ? 'SQS' : 'API Gateway');

    try {
        if ('Records' in event && event.Records?.length) {
            // SQS Event Processing
            console.log(`📥 Processing ${event.Records.length} SQS messages`);

            for (const record of event.Records) {
                try {
                    const requestData = JSON.parse(record.body) as XeroRequestData;
                    console.log(`🔄 Processing Trial Balance sync for company: ${requestData.companyId}`);

                    await processTrialBalanceRequest(requestData, context, "SYSTEM");

                    console.log(`✅ Successfully processed Trial Balance sync for company: ${requestData.companyId}`);
                } catch (error: any) {
                    console.error(`❌ Failed to process SQS message for company: ${JSON.parse(record.body).companyId}`, {
                        error: error.message,
                        stack: error.stack,
                        messageId: record.messageId
                    });
                    throw error; // Trigger Lambda retry mechanism
                }
            }

            console.log('✅ All SQS messages processed successfully');
            return; // No return value for SQS events

        } else {
            // API Gateway Event Processing
            console.log('🌐 Processing API Gateway request');

            try {
                const requestData = parseRequestData(event as APIGatewayProxyEvent);
                console.log(`🔄 Starting Trial Balance sync for company: ${requestData.companyId}`);

                const startTime = Date.now();
                await processTrialBalanceRequest(requestData, context, 'USER');
                const duration = Date.now() - startTime;

                console.log(`✅ Trial Balance sync completed in ${duration}ms for company: ${requestData.companyId}`);

                return {
                    statusCode: 200,
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Processing-Time': duration.toString()
                    },
                    body: JSON.stringify({
                        success: true,
                        message: '✅ Trial Balance data synchronized successfully',
                        timestamp: new Date().toISOString(),
                        processingTimeMs: duration,
                        companyId: requestData.companyId
                    }),
                };

            } catch (error: any) {
                console.error('❌ API Gateway request failed:', {
                    error: error.message,
                    stack: error.stack,
                    requestId: context.awsRequestId
                });

                // Determine appropriate HTTP status code
                let statusCode = 500;
                if (error instanceof ValidationError) {
                    statusCode = 400;
                } else if (error.message.includes('not found')) {
                    statusCode = 404;
                } else if (error.message.includes('authentication') || error.message.includes('token')) {
                    statusCode = 401;
                } else if (error.message.includes('rate limit')) {
                    statusCode = 429;
                }

                return {
                    statusCode,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        success: false,
                        error: error.message,
                        timestamp: new Date().toISOString(),
                        requestId: context.awsRequestId
                    }),
                };
            }
        }

    } catch (error: any) {
        console.error('❌ Unexpected handler error:', {
            error: error.message,
            stack: error.stack,
            requestId: context.awsRequestId
        });

        // For SQS events, throw to trigger retry
        if ('Records' in event) {
            throw error;
        }

        // For API Gateway events, return error response
        return {
            statusCode: 500,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                success: false,
                error: 'Internal server error occurred during Trial Balance synchronization',
                timestamp: new Date().toISOString(),
                requestId: context.awsRequestId
            }),
        };
    }
};
