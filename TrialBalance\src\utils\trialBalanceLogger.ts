/**
 * Trial Balance Logger Utility
 *
 * This utility provides simplified logging functionality specifically for
 * Trial Balance Lambda function execution using only the IntegrationLog model.
 * It bypasses the missing ApiLog and SyncLog models.
 *
 * Key Features:
 * - Lambda function execution logging (success/failure)
 * - Integration with existing IntegrationLog model
 * - Functional implementation
 * - Comprehensive error handling
 *
 * <AUTHOR> Balance Logger Utility
 * @version 1.0.0
 */

import {
    createLambdaContext,
    logLambdaStart,
    logLambdaSuccess,
    logLambdaFailure,
    createSyncSummary,
    LambdaExecutionContext,
    SyncSummary,
    formatDuration
} from './integrationLogger';

/**
 * Trial Balance Execution Result Interface
 */
export interface TrialBalanceExecutionResult {
    processedMonths: number;
    totalRecords: number;
    apiCalls: number;
    duration: string;
    errors: number;
    warnings: number;
}

/**
 * Format duration helper
 */
function formatDurationMs(durationMs: number): string {
    return formatDuration(durationMs);
}

/**
 * Create detailed execution result
 */
export function createDetailedExecutionResult(
    processedMonths: number,
    totalRecords: number,
    apiCalls: number,
    duration: string,
    errors: number = 0,
    warnings: number = 0
): TrialBalanceExecutionResult {
    return {
        processedMonths,
        totalRecords,
        apiCalls,
        duration,
        errors,
        warnings,
    };
}

/**
 * Start Trial Balance Lambda execution logging
 */
export async function startTrialBalanceExecution(
    companyId: string,
    triggeredBy: 'USER' | 'SYSTEM' = 'SYSTEM'
): Promise<{ context: LambdaExecutionContext; logId: string }> {
    console.log(`🚀 Starting Trial Balance execution logging for company: ${companyId}`);

    try {
        // Create Lambda execution context
        const context = createLambdaContext(companyId, 'TrialBalance', triggeredBy);

        // Log Lambda start
        const logId = await logLambdaStart(context);

        console.log(`📝 Trial Balance execution started - Log ID: ${logId}, Request ID: ${context.requestId}`);

        return { context, logId };
    } catch (error) {
        console.error('❌ Failed to start Trial Balance execution logging:', error);
        throw error;
    }
}

/**
 * Log Trial Balance execution success
 */
export async function logTrialBalanceSuccess(
    logId: string,
    context: LambdaExecutionContext,
    executionResult: TrialBalanceExecutionResult
): Promise<void> {
    try {
        console.log(`✅ Logging Trial Balance success for Log ID: ${logId}`);

        // Create sync summary from execution result
        const syncSummary = createSyncSummary(
            executionResult.processedMonths,
            executionResult.totalRecords,
            executionResult.apiCalls,
            executionResult.duration,
            executionResult.errors,
            executionResult.warnings
        );

        // Log Lambda success
        await logLambdaSuccess(logId, context, syncSummary);

        console.log(`📊 Trial Balance execution completed successfully:`, {
            processedMonths: executionResult.processedMonths,
            totalRecords: executionResult.totalRecords,
            apiCalls: executionResult.apiCalls,
            duration: executionResult.duration,
            errors: executionResult.errors,
            warnings: executionResult.warnings,
        });

    } catch (logError) {
        console.error('❌ Failed to log Trial Balance success:', logError);
        // Don't throw here to avoid masking the original success
    }
}

/**
 * Log Trial Balance execution failure
 */
export async function logTrialBalanceFailure(
    logId: string,
    context: LambdaExecutionContext,
    error: Error,
    partialResult?: Partial<TrialBalanceExecutionResult>
): Promise<void> {
    try {
        console.log(`❌ Logging Trial Balance failure for Log ID: ${logId}`);

        // Create partial sync summary if available
        const syncSummary = partialResult ? {
            processedMonths: partialResult.processedMonths || 0,
            totalRecords: partialResult.totalRecords || 0,
            apiCalls: partialResult.apiCalls || 0,
            duration: partialResult.duration || formatDurationMs(Date.now() - context.startTime),
            errors: (partialResult.errors || 0) + 1, // Add current error
            warnings: partialResult.warnings || 0,
        } : undefined;

        // Log Lambda failure
        await logLambdaFailure(logId, context, error, syncSummary);

        console.error(`💥 Trial Balance execution failed:`, {
            error: error.message,
            stack: error.stack,
            partialResult: syncSummary,
        });

    } catch (logError) {
        console.error('❌ Failed to log Trial Balance failure:', logError);
        // Don't throw here to avoid masking the original error
    }
}

/**
 * Wrapper function for complete Trial Balance execution with logging
 */
export async function executeWithLogging<T>(
    companyId: string,
    operation: () => Promise<T>,
    createResult: (data: T, duration: string) => TrialBalanceExecutionResult,
    triggeredBy: 'USER' | 'SYSTEM' = 'SYSTEM'
): Promise<T> {
    const startTime = Date.now();
    let logId: string | null = null;
    let context: LambdaExecutionContext | null = null;

    try {
        // Start execution logging
        const logInfo = await startTrialBalanceExecution(companyId, triggeredBy);
        logId = logInfo.logId;
        context = logInfo.context;

        // Execute the operation
        const result = await operation();

        // Calculate duration
        const duration = formatDurationMs(Date.now() - startTime);

        // Create execution result
        const executionResult = createResult(result, duration);

        // Log success
        await logTrialBalanceSuccess(logId, context, executionResult);

        return result;

    } catch (error) {
        // Calculate duration
        const duration = formatDurationMs(Date.now() - startTime);

        // Log failure
        if (logId && context) {
            await logTrialBalanceFailure(logId, context, error as Error, {
                duration,
                errors: 1
            });
        }

        throw error;
    }
}

/**
 * Simple execution wrapper without result transformation
 */
export async function executeTrialBalanceWithLogging(
    companyId: string,
    operation: () => Promise<void>,
    processedMonths: number = 0,
    totalRecords: number = 0,
    apiCalls: number = 0,
    triggeredBy: 'USER' | 'SYSTEM' = 'SYSTEM'
): Promise<void> {
    await executeWithLogging(
        companyId,
        operation,
        (_, duration) => createDetailedExecutionResult(
            processedMonths,
            totalRecords,
            apiCalls,
            duration
        ),
        triggeredBy
    );
}

// Removed individual API call logging - using only main Lambda execution logging

/**
 * Helper to get current timestamp for logging
 */
export function getCurrentTimestamp(): Date {
    return new Date();
}

/**
 * Helper to calculate duration between timestamps
 */
export function calculateDuration(startTime: Date, endTime?: Date): string {
    const end = endTime || new Date();
    const durationMs = end.getTime() - startTime.getTime();
    return formatDurationMs(durationMs);
}
