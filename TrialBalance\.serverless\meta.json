{"D:\\Tushar\\self_learning\\lmabda micro\\TrialBalance\\serverless.yml": {"versionFramework": "4.17.1", "servicePath": "D:\\Tushar\\self_learning\\lmabda micro\\TrialBalance\\serverless.yml", "serviceConfigFileName": "serverless.yml", "service": {"service": "trial-balance-lambda", "frameworkVersion": "4", "provider": {"name": "aws", "runtime": "nodejs18.x", "region": "us-east-1", "memorySize": 1024, "architecture": "x86_64", "tracing": {"lambda": true}, "timeout": 500, "environment": {"DATABASE_URL": "postgresql://postgres:<EMAIL>:5432/furge?schema=public", "XERO_CLIENT_ID": "DB9419E9BC6D4B989B90B960B2EFB2A3", "XERO_CLIENT_SECRET": "<REDACTED>", "XERO_TOKEN_URL": "<REDACTED>", "XERO_BASE_URL": "https://api.xero.com/api.xro/2.0/", "FIRST_RETRY": "10", "SECOND_RETRY": "20", "LAST_RETRY": "30", "REGION": "us-east-1", "ACCESS_KEY_ID": "<REDACTED>", "SECRET_ACCESS_KEY": "<REDACTED>", "IS_OFFLINE": "true", "PRISMA_QUERY_ENGINE_LIBRARY": "/var/task/node_modules/.prisma/client/libquery_engine-rhel-openssl-1.0.x.so.node"}, "stage": "dev", "versionFunctions": true}, "functions": {"TrialBalanceSyncHandler": {"handler": "src/handlers/xeroTrialBalanceHandler.handler", "environment": {"IS_OFFLINE": "true"}, "events": [{"sqs": {"arn": {"Fn::GetAtt": ["BalanceSheetSyncQueue", "<PERSON><PERSON>"]}, "batchSize": 5, "maximumRetryAttempts": 3}}, {"http": {"path": "xero/sync-trial-balance", "method": "post"}}], "name": "trial-balance-lambda-dev-TrialBalanceSyncHandler"}}, "package": {"patterns": ["!node_modules/.prisma/client/libquery_engine-darwin*", "!node_modules/.prisma/client/libquery_engine-windows*", "!node_modules/.prisma/client/libquery_engine-arm*", "!node_modules/.prisma/client/libquery_engine-debian*", "!node_modules/.prisma/client/libquery_engine-rhel-openssl-3.0.x*", "!node_modules/@prisma/engines/**", "node_modules/.prisma/**", "src/**", "node_modules/**", "!node_modules/prisma/**"], "artifactsS3KeyDirname": "serverless/trial-balance-lambda/dev/code-artifacts"}, "resources": {"Resources": {"BalanceSheetSyncQueue": {"Type": "AWS::SQS::Queue", "Properties": {"QueueName": "balance-sheet-sync-queue"}}}}, "plugins": ["serverless-offline"], "custom": {"serverless-offline": {"httpPort": 8000}, "dotenv": {"path": ".env"}}}, "provider": {"name": "aws", "runtime": "nodejs18.x", "region": "us-east-1", "memorySize": 1024, "architecture": "x86_64", "tracing": {"lambda": true}, "timeout": 500, "environment": {"DATABASE_URL": "postgresql://postgres:<EMAIL>:5432/furge?schema=public", "XERO_CLIENT_ID": "DB9419E9BC6D4B989B90B960B2EFB2A3", "XERO_CLIENT_SECRET": "<REDACTED>", "XERO_TOKEN_URL": "<REDACTED>", "XERO_BASE_URL": "https://api.xero.com/api.xro/2.0/", "FIRST_RETRY": "10", "SECOND_RETRY": "20", "LAST_RETRY": "30", "REGION": "us-east-1", "ACCESS_KEY_ID": "<REDACTED>", "SECRET_ACCESS_KEY": "<REDACTED>", "IS_OFFLINE": "true", "PRISMA_QUERY_ENGINE_LIBRARY": "/var/task/node_modules/.prisma/client/libquery_engine-rhel-openssl-1.0.x.so.node"}, "stage": "dev", "versionFunctions": true}, "dashboard": {"isEnabledForService": false, "requiredAuthentication": false, "orgFeaturesInUse": null, "orgObservabilityIntegrations": null, "serviceAppId": null, "serviceProvider": null, "instanceParameters": null}, "error": {"message": "Build failed with 1 error:\nsrc/services/refreshTokenService.ts:10:55: ERROR: Could not resolve \"./apiLogService\"", "stack": "ServerlessError2: Build failed with 1 error:\nsrc/services/refreshTokenService.ts:10:55: ERROR: Could not resolve \"./apiLogService\"\n    at Esbuild._build (file:///C:/Users/<USER>/.serverless/releases/4.17.1/package/dist/sf-core.js:1368:16014)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async before:offline:start (file:///C:/Users/<USER>/.serverless/releases/4.17.1/package/dist/sf-core.js:1368:4650)\n    at async PluginManager.runHooks (file:///C:/Users/<USER>/.serverless/releases/4.17.1/package/dist/sf-core.js:1377:9870)\n    at async PluginManager.invoke (file:///C:/Users/<USER>/.serverless/releases/4.17.1/package/dist/sf-core.js:1377:10580)\n    at async PluginManager.run (file:///C:/Users/<USER>/.serverless/releases/4.17.1/package/dist/sf-core.js:1377:11373)\n    at async Serverless.run (file:///C:/Users/<USER>/.serverless/releases/4.17.1/package/dist/sf-core.js:1384:10527)\n    at async runFramework (file:///C:/Users/<USER>/.serverless/releases/4.17.1/package/dist/sf-core.js:1411:1777)\n    at async TraditionalRunner.run (file:///C:/Users/<USER>/.serverless/releases/4.17.1/package/dist/sf-core.js:1407:28336)\n    at async route (file:///C:/Users/<USER>/.serverless/releases/4.17.1/package/dist/sf-core.js:1582:2878)", "code": "ESBULD_BUILD_ERROR"}, "serviceRawFile": "service: trial-balance-lambda\n\nframeworkVersion: '4'\n\nprovider:\n  name: aws\n  runtime: nodejs18.x\n  region: us-east-1\n  memorySize: 1024\n  architecture: x86_64\n  tracing:\n    lambda: true\n  timeout: 500\n  environment:\n    DATABASE_URL: ${env:DATABASE_URL}\n    XERO_CLIENT_ID: ${env:XERO_CLIENT_ID}\n    XERO_CLIENT_SECRET: ${env:XERO_CLIENT_SECRET}\n    XERO_TOKEN_URL: ${env:XERO_TOKEN_URL}\n    XERO_BASE_URL: ${env:XERO_BASE_URL}\n    FIRST_RETRY: ${env:FIRST_RETRY}\n    SECOND_RETRY: ${env:SECOND_RETRY}\n    LAST_RETRY: ${env:LAST_RETRY}\n    REGION: ${env:REGION}\n    ACCESS_KEY_ID: ${env:ACCESS_KEY_ID}\n    SECRET_ACCESS_KEY: ${env:SECRET_ACCESS_KEY}\n    IS_OFFLINE: ${env:IS_OFFLINE, 'false'}\n    PRISMA_QUERY_ENGINE_LIBRARY: /var/task/node_modules/.prisma/client/libquery_engine-rhel-openssl-1.0.x.so.node\n\nfunctions:\n  TrialBalanceSyncHandler:\n    handler: src/handlers/xeroTrialBalanceHandler.handler\n    environment:\n      IS_OFFLINE: ${env:IS_OFFLINE, 'true'}\n    events:\n      - sqs:\n          arn:\n            Fn::GetAtt:\n              - BalanceSheetSyncQueue\n              - Arn\n          batchSize: 5\n          maximumRetryAttempts: 3\n      - http:\n          path: xero/sync-trial-balance\n          method: post\n\npackage:\n  patterns:\n    # Exclude unnecessary prisma query engines for other platforms\n    - '!node_modules/.prisma/client/libquery_engine-darwin*'\n    - '!node_modules/.prisma/client/libquery_engine-windows*'\n    - '!node_modules/.prisma/client/libquery_engine-arm*'\n    - '!node_modules/.prisma/client/libquery_engine-debian*'\n    - '!node_modules/.prisma/client/libquery_engine-rhel-openssl-3.0.x*'\n    - '!node_modules/@prisma/engines/**'\n    # Include only necessary files\n    - 'node_modules/.prisma/**'\n    - 'src/**'\n    - 'node_modules/**'\n    - '!node_modules/prisma/**'\n\nresources:\n  Resources:\n    BalanceSheetSyncQueue:\n      Type: AWS::SQS::Queue\n      Properties:\n        QueueName: balance-sheet-sync-queue\n\nplugins:\n  - serverless-offline\n\ncustom:\n  serverless-offline:\n    httpPort: 8000\n  dotenv:\n    path: .env\n", "command": ["offline"], "options": {}, "orgId": "********-05ed-463b-9c61-6a42b9e0a0fb", "orgName": "testor<PERSON>us", "userId": "75cWDV7wPcW0B3jgnf", "userName": "testor<PERSON>us", "serviceProviderAwsAccountId": "************", "serviceProviderAwsCfStackId": null, "serviceProviderAwsCfStackCreated": null, "serviceProviderAwsCfStackUpdated": null, "serviceProviderAwsCfStackStatus": null, "serviceProviderAwsCfStackOutputs": null}}