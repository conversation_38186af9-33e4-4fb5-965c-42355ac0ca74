/**
 * Integration Logger Utility for Profit & Loss Service
 *
 * This utility provides comprehensive logging functionality for Profit & Loss
 * synchronization operations using the IntegrationLog model.
 *
 * Key Features:
 * - Lambda function execution logging (success/failure)
 * - API call tracking with detailed metrics
 * - Sync operation monitoring with status management
 * - Error handling and diagnostic information
 * - Performance metrics and timing data
 *
 * <AUTHOR> & Loss Integration Logger
 * @version 1.0.0
 */

import { PrismaClient } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';
import moment from 'moment';

/**
 * Sync Status Enum (matching Prisma schema)
 */
export enum SyncStatus {
    PENDING = 'PENDING',
    IN_PROGRESS = 'IN_PROGRESS',
    SUCCESS = 'SUCCESS',
    WARNING = 'WARNING',
    ERROR = 'ERROR',
    RETRYING = 'RETRYING',
    CANCELLED = 'CANCELLED'
}

/**
 * Integration Log Data Interface
 */
export interface IntegrationLogData {
    requestId?: string;
    companyId: string;
    apiName: string;
    method?: string;
    apiUrl?: string;
    integrationName?: string;
    statusCode?: string;
    duration?: string;
    message?: string;
    entity?: string;
    triggeredBy?: 'USER' | 'SYSTEM';
    syncStatus?: SyncStatus;
    apiRequest?: any;
    apiResponse?: any;
    errorDetails?: any;
    startedAt?: Date;
    completedAt?: Date;
}

/**
 * Lambda Execution Context
 */
export interface LambdaExecutionContext {
    requestId: string;
    companyId: string;
    entity: string;
    triggeredBy: 'USER' | 'SYSTEM';
    startTime: number;
}

/**
 * Sync Summary Interface
 */
export interface SyncSummary {
    monthsProcessed: number;
    totalMonths: number;
    trackingRecords: number;
    summaryRecords: number;
    apiCalls: number;
    duration: string;
    errors: number;
    warnings: number;
}

let prisma: PrismaClient | null = null;

/**
 * Get or create Prisma client instance
 */
function getPrismaClient(): PrismaClient {
    if (!prisma) {
        const config = {
            log: ['error', 'warn'] as Array<'error' | 'warn'>,
            errorFormat: 'pretty' as const,
        };
        if (process.env['IS_OFFLINE'] === 'true') {
            delete process.env['PRISMA_QUERY_ENGINE_LIBRARY'];
        }
        prisma = new PrismaClient(config);
    }
    return prisma;
}

/**
 * Create a new integration log entry
 */
export async function createIntegrationLog(logData: IntegrationLogData): Promise<string> {
    try {
        const integrationLog = await getPrismaClient().integrationLog.create({
            data: {
                RequestId: logData.requestId || uuidv4(),
                CompanyId: logData.companyId,
                ApiName: logData.apiName,
                Method: logData.method || null,
                ApiUrl: logData.apiUrl || null,
                IntegrationName: logData.integrationName || 'Xero',
                StatusCode: logData.statusCode || null,
                Duration: logData.duration || null,
                Message: logData.message || null,
                Entity: logData.entity || null,
                TriggeredBy: logData.triggeredBy || 'SYSTEM',
                SyncStatus: logData.syncStatus || SyncStatus.PENDING,
                ApiRequest: logData.apiRequest || null,
                ApiResponse: logData.apiResponse || null,
                ErrorDetails: logData.errorDetails || null,
                StartedAt: logData.startedAt || new Date(),
                CompletedAt: logData.completedAt || null,
            },
        });

        return integrationLog.Id;
    } catch (error: any) {
        console.error('❌ Failed to create integration log:', {
            error: error.message,
            companyId: logData.companyId,
            apiName: logData.apiName,
        });
        throw error;
    }
}

/**
 * Create Lambda execution context
 */
export function createLambdaContext(
    companyId: string,
    entity: string,
    triggeredBy: 'USER' | 'SYSTEM' = 'SYSTEM'
): LambdaExecutionContext {
    return {
        requestId: uuidv4(),
        companyId,
        entity,
        triggeredBy,
        startTime: Date.now(),
    };
}

/**
 * Log Lambda function start
 */
export async function logLambdaStart(context: LambdaExecutionContext): Promise<string> {
    const logData: IntegrationLogData = {
        requestId: context.requestId,
        companyId: context.companyId,
        apiName: 'ProfitLossSync',
        method: 'POST',
        apiUrl: `/xero/sync-profit-loss`,
        integrationName: 'Xero',
        message: `Profit & Loss sync started for ${context.entity}`,
        entity: context.entity,
        triggeredBy: context.triggeredBy,
        syncStatus: SyncStatus.IN_PROGRESS,
        startedAt: new Date(context.startTime),
    };
    console.log(`🚀 Starting Profit & Loss sync for company:`, logData);

    return await createIntegrationLog(logData);
}

/**
 * Create sync summary
 */
export function createSyncSummary(
    monthsProcessed: number,
    totalMonths: number,
    trackingRecords: number,
    summaryRecords: number,
    apiCalls: number,
    duration: string,
    errors: number,
    warnings: number
): SyncSummary {
    return {
        monthsProcessed,
        totalMonths,
        trackingRecords,
        summaryRecords,
        apiCalls,
        duration,
        errors,
        warnings
    };
}

/**
 * Log Lambda function success
 */
export async function logLambdaSuccess(
    logId: string,
    context: LambdaExecutionContext,
    summary: SyncSummary
): Promise<void> {
    try {
        await getPrismaClient().integrationLog.update({
            where: { Id: logId },
            data: {
                SyncStatus: SyncStatus.SUCCESS,
                Message: `Profit & Loss sync completed successfully. Processed ${summary.monthsProcessed}/${summary.totalMonths} months in ${summary.duration}`,
                Duration: summary.duration,
                SyncSummary: { ...summary },
                CompletedAt: new Date(),
            },
        });

        console.log(`✅ Lambda success logged for request: ${context.requestId}`);
    } catch (error: any) {
        console.error('❌ Failed to log Lambda success:', error);
        throw error;
    }
}

/**
 * Log Lambda function failure
 */
export async function logLambdaFailure(
    logId: string,
    context: LambdaExecutionContext,
    error: Error,
    summary?: SyncSummary
): Promise<void> {
    try {
        await getPrismaClient().integrationLog.update({
            where: { Id: logId },
            data: {
                SyncStatus: SyncStatus.ERROR,
                Message: `Profit & Loss sync failed: ${error.message}`,
                Duration: summary?.duration || null,
                ErrorDetails: {
                    message: error.message,
                    stack: error.stack,
                    name: error.name,
                },
                ApiResponse: { ...summary },
                CompletedAt: new Date(),
            },
        });

        console.log(`❌ Lambda failure logged for request: ${context.requestId}`);
    } catch (logError: any) {
        console.error('❌ Failed to log Lambda failure:', logError);
        throw logError;
    }
}
