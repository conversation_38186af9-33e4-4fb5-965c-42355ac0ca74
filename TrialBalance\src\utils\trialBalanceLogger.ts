/**
 * Trial Balance Logger Utility
 *
 * This utility provides simplified logging functionality specifically for
 * Trial Balance Lambda function execution using only the IntegrationLog model.
 * It bypasses the missing ApiLog and SyncLog models.
 *
 * Key Features:
 * - Lambda function execution logging (success/failure)
 * - Integration with existing IntegrationLog model
 * - Functional implementation
 * - Comprehensive error handling
 *
 * <AUTHOR> Balance Logger Utility
 * @version 1.0.0
 */

import {
    logLambdaInvocation,
    updateLambdaSuccess,
    updateLambdaError,
    formatDuration
} from './integrationLogger';

/**
 * Start Trial Balance Lambda execution logging
 */
export async function startTrialBalanceExecution(
    companyId: string,
    triggeredBy: 'USER' | 'SYSTEM' = 'SYSTEM'
): Promise<{ logId: string; startTime: number }> {
    try {
        const startTime = Date.now();
        const logId = await logLambdaInvocation(companyId, 'TrialBalance', triggeredBy);

        return { logId, startTime };
    } catch (error) {
        console.error('❌ Failed to start Trial Balance execution logging:', error);
        throw error;
    }
}

/**
 * Log Trial Balance execution success
 */
export async function logTrialBalanceSuccess(
    logId: string,
    startTime: number,
    processedMonths: number,
    totalRecords: number = 0
): Promise<void> {
    try {
        const duration = formatDuration(Date.now() - startTime);
        const message = `Trial Balance(Report) sync successful. Processed ${processedMonths} months with ${totalRecords} total records.`;

        const syncSummary = {
            processedMonths,
            totalRecords,
            duration,
            errors: 0,
            warnings: 0
        };

        await updateLambdaSuccess(logId, duration, message, syncSummary);

    } catch (logError) {
        console.error('❌ Failed to log Trial Balance success:', logError);
        // Don't throw here to avoid masking the original success
    }
}

/**
 * Log Trial Balance execution failure
 */
export async function logTrialBalanceFailure(
    logId: string,
    startTime: number,
    error: Error,
    processedMonths: number = 0,
    totalRecords: number = 0
): Promise<void> {
    try {
        const duration = formatDuration(Date.now() - startTime);

        const partialSummary = {
            processedMonths,
            totalRecords,
            duration,
            errors: 1,
            warnings: 0
        };

        await updateLambdaError(logId, duration, error, partialSummary);

    } catch (logError) {
        console.error('❌ Failed to log Trial Balance failure:', logError);
        // Don't throw here to avoid masking the original error
    }
}

// Simplified Trial Balance logging - only Lambda invocation level logging
