/**
 * Integration Logger Utility for Trial Balance Service
 *
 * This utility provides comprehensive logging functionality for Trial Balance
 * synchronization operations using the IntegrationLog model.
 *
 * Key Features:
 * - Lambda function execution logging (success/failure)
 * - API call tracking with detailed metrics
 * - Sync operation monitoring with status management
 * - Error handling and diagnostic information
 * - Performance metrics and timing data
 *
 * <AUTHOR> Balance Integration Logger
 * @version 1.0.0
 */

import { PrismaClient } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';
import dotenv from 'dotenv';
dotenv.config();

/**
 * Sync Status enum matching Prisma schema
 */
export enum SyncStatus {
    PENDING = 'PENDING',
    IN_PROGRESS = 'IN_PROGRESS',
    SUCCESS = 'SUCCESS',
    WARNING = 'WARNING',
    ERROR = 'ERROR',
    RETRYING = 'RETRYING',
    CANCELLED = 'CANCELLED'
}

/**
 * Integration Log Data Interface
 */
export interface IntegrationLogData {
    requestId?: string;
    companyId: string;
    apiName: string;
    method?: string;
    apiUrl?: string;
    integrationName?: string;
    statusCode?: string;
    duration?: string;
    message?: string;
    entity?: string;
    triggeredBy?: 'USER' | 'SYSTEM';
    syncStatus?: SyncStatus;
    apiRequest?: any;
    apiResponse?: any;
    errorDetails?: any;
    syncSummary?: any;
    startedAt?: Date;
    completedAt?: Date;
}

/**
 * Lambda Execution Context
 */
export interface LambdaExecutionContext {
    requestId: string;
    companyId: string;
    functionName: string;
    startTime: number;
    entity: string;
    triggeredBy: 'USER' | 'SYSTEM';
}

/**
 * Sync Summary Interface
 */
export interface SyncSummary {
    processedMonths: number;
    totalRecords: number;
    apiCalls: number;
    duration: string;
    errors: number;
    warnings: number;
}

let prisma: PrismaClient | null = null;

/**
 * Get or create Prisma client instance
 */
function getPrismaClient(): PrismaClient {
    if (!prisma) {
        const config = {
            log: ['error', 'warn'] as Array<'error' | 'warn'>,
            errorFormat: 'pretty' as const,
        };
        if (process.env['IS_OFFLINE'] === 'true') {
            delete process.env['PRISMA_QUERY_ENGINE_LIBRARY'];
        }
        prisma = new PrismaClient(config);
    }
    return prisma;
}

/**
 * Format duration from milliseconds to human readable format
 */
export function formatDuration(durationMs: number): string {
    if (durationMs < 1000) {
        return `${durationMs}ms`;
    } else if (durationMs < 60000) {
        return `${(durationMs / 1000).toFixed(1)}s`;
    } else {
        const minutes = Math.floor(durationMs / 60000);
        const seconds = Math.floor((durationMs % 60000) / 1000);
        return `${minutes}m ${seconds}s`;
    }
}

/**
 * Create a new integration log entry
 */
export async function createIntegrationLog(logData: IntegrationLogData): Promise<string> {
    try {
        const integrationLog = await getPrismaClient().integrationLog.create({
            data: {
                RequestId: logData.requestId || uuidv4(),
                CompanyId: logData.companyId,
                ApiName: logData.apiName,
                Method: logData.method || null,
                ApiUrl: logData.apiUrl || null,
                IntegrationName: logData.integrationName || 'Xero',
                StatusCode: logData.statusCode || null,
                Duration: logData.duration || null,
                Message: logData.message || null,
                Entity: logData.entity || null,
                TriggeredBy: logData.triggeredBy || 'SYSTEM',
                SyncStatus: logData.syncStatus || SyncStatus.PENDING,
                ApiRequest: logData.apiRequest || null,
                ApiResponse: logData.apiResponse || null,
                ErrorDetails: logData.errorDetails || null,
                SyncSummary: logData.syncSummary || null,
                StartedAt: logData.startedAt || new Date(),
                CompletedAt: logData.completedAt || null,
                CreatedAt: new Date(),
                UpdatedAt: new Date(),
            },
        });

        console.log(`✅ Integration log created with ID: ${integrationLog.Id}`);
        return integrationLog.Id;
    } catch (error: any) {
        console.error('❌ Failed to create integration log:', {
            error: error.message,
            apiName: logData.apiName,
            companyId: logData.companyId,
        });
        throw error;
    }
}

/**
 * Update an existing integration log entry
 */
export async function updateIntegrationLog(
    logId: string,
    updateData: Partial<IntegrationLogData>
): Promise<void> {
    try {
        console.log(`📝 Updating integration log ${logId} with status: ${updateData.syncStatus}`);

        await getPrismaClient().integrationLog.update({
            where: { Id: logId },
            data: {
                ...(updateData.statusCode && { StatusCode: updateData.statusCode }),
                ...(updateData.duration && { Duration: updateData.duration }),
                ...(updateData.message && { Message: updateData.message }),
                ...(updateData.syncStatus && { SyncStatus: updateData.syncStatus }),
                ...(updateData.apiRequest && { ApiRequest: updateData.apiRequest }),
                ...(updateData.apiResponse && { ApiResponse: updateData.apiResponse }),
                ...(updateData.errorDetails && { ErrorDetails: updateData.errorDetails }),
                ...(updateData.syncSummary && { SyncSummary: updateData.syncSummary }),
                ...(updateData.completedAt && { CompletedAt: updateData.completedAt }),
                UpdatedAt: new Date(),
            },
        });

        console.log(`✅ Integration log ${logId} updated successfully`);
    } catch (error: any) {
        console.error('❌ Failed to update integration log:', {
            error: error.message,
            logId,
            status: updateData.syncStatus,
        });
        // Don't throw error to avoid breaking the main flow
    }
}

/**
 * Create Lambda execution context
 */
export function createLambdaContext(
    companyId: string,
    entity: string,
    triggeredBy: 'USER' | 'SYSTEM' = 'SYSTEM'
): LambdaExecutionContext {
    return {
        requestId: uuidv4(),
        companyId,
        functionName: 'TrialBalanceSync',
        startTime: Date.now(),
        entity,
        triggeredBy,
    };
}

/**
 * Create sync summary
 */
export function createSyncSummary(
    processedMonths: number,
    totalRecords: number,
    apiCalls: number,
    duration: string,
    errors: number = 0,
    warnings: number = 0
): SyncSummary {
    return {
        processedMonths,
        totalRecords,
        apiCalls,
        duration,
        errors,
        warnings,
    };
}

/**
 * Log Lambda function start
 */
export async function logLambdaStart(context: LambdaExecutionContext): Promise<string> {
    const logData: IntegrationLogData = {
        requestId: context.requestId,
        companyId: context.companyId,
        apiName: 'TrialBalanceSync',
        method: 'POST',
        apiUrl: `/xero/sync-trial-balance`,
        integrationName: 'Xero',
        message: `Trial Balance sync started for ${context.entity}`,
        entity: context.entity,
        triggeredBy: context.triggeredBy,
        syncStatus: SyncStatus.IN_PROGRESS,
        startedAt: new Date(context.startTime),
    };
    console.log(`🚀 Starting Trial Balance sync for company:`, logData);

    return await createIntegrationLog(logData);
}

/**
 * Log Lambda function success
 */
export async function logLambdaSuccess(
    logId: string,
    context: LambdaExecutionContext,
    syncSummary: SyncSummary
): Promise<void> {
    const duration = Date.now() - context.startTime;
    const durationFormatted = formatDuration(duration);

    const updateData: Partial<IntegrationLogData> = {
        statusCode: '200',
        duration: durationFormatted,
        message: `Trial Balance sync completed successfully. Processed ${syncSummary.processedMonths} months with ${syncSummary.totalRecords} total records.`,
        syncStatus: syncSummary.warnings > 0 ? SyncStatus.WARNING : SyncStatus.SUCCESS,
        syncSummary: syncSummary,
        completedAt: new Date(),
    };

    await updateIntegrationLog(logId, updateData);
    console.log(`✅ Trial Balance sync completed successfully in ${durationFormatted}`);
}

/**
 * Log Lambda function failure
 */
export async function logLambdaFailure(
    logId: string,
    context: LambdaExecutionContext,
    error: Error,
    syncSummary?: Partial<SyncSummary>
): Promise<void> {
    const duration = Date.now() - context.startTime;
    const durationFormatted = formatDuration(duration);

    const errorDetails = {
        message: error.message,
        stack: error.stack,
        name: error.name,
    };

    const updateData: Partial<IntegrationLogData> = {
        statusCode: '500',
        duration: durationFormatted,
        message: `Trial Balance sync failed: ${error.message}`,
        syncStatus: SyncStatus.ERROR,
        errorDetails: errorDetails,
        syncSummary: syncSummary,
        completedAt: new Date(),
    };

    await updateIntegrationLog(logId, updateData);
    console.error(`❌ Trial Balance sync failed after ${durationFormatted}: ${error.message}`);
}

/**
 * Log API call details
 */
export async function logApiCall(
    requestId: string,
    companyId: string,
    apiName: string,
    method: string,
    url: string,
    statusCode: number,
    duration: number,
    request?: any,
    response?: any,
    error?: any
): Promise<string> {
    const logData: IntegrationLogData = {
        requestId,
        companyId,
        apiName,
        method,
        apiUrl: url,
        integrationName: 'Xero',
        statusCode: statusCode.toString(),
        duration: `${duration}ms`,
        message: error ? `API call failed: ${error.message}` : `Trial Balance(Report) sync successful`,
        entity: 'API',
        triggeredBy: 'SYSTEM',
        syncStatus: error ? SyncStatus.ERROR : SyncStatus.SUCCESS,
        apiRequest: request,
        apiResponse: response,
        errorDetails: error,
        startedAt: new Date(),
        completedAt: new Date(),
    };

    return await createIntegrationLog(logData);
}

/**
 * Get integration logs for a company
 */
export async function getIntegrationLogs(
    companyId: string,
    entity?: string,
    syncStatus?: SyncStatus,
    limit: number = 50
) {
    try {
        const where: any = { CompanyId: companyId };
        if (entity) where.Entity = entity;
        if (syncStatus) where.SyncStatus = syncStatus;

        return await getPrismaClient().integrationLog.findMany({
            where,
            orderBy: {
                CreatedAt: 'desc',
            },
            take: limit,
            select: {
                Id: true,
                RequestId: true,
                ApiName: true,
                Entity: true,
                IntegrationName: true,
                SyncStatus: true,
                Message: true,
                Duration: true,
                StartedAt: true,
                CompletedAt: true,
                CreatedAt: true,
            },
        });
    } catch (error: any) {
        console.error('❌ Failed to retrieve integration logs:', {
            error: error.message,
            companyId,
        });
        return [];
    }
}
